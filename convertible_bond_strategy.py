#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
月频可转债量化策略
实现所有筛选条件的可转债投资策略，包括：
1. 正股收盘价 ≥ 4元
2. 转债余额 ≥ 5亿元
3. 正股市值 ≥ 40亿元
4. 转债剩余期限 ≥ 1年
5. 转债评级 ≥ A+
6. 到期收益率(YTM)排名前20%
7. 排除亏损股票（正股净利润为负的剔除）
8. 过去20个交易日日均成交额 ≥ 1000万元
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import warnings
from typing import Dict, List, Tuple, Optional
from get_db_funs import get_db_data
from strategy_analyzer import StrategyAnalyzer
import sys

warnings.filterwarnings('ignore')

class ConvertibleBondStrategy:
    """可转债量化策略类"""
    
    def __init__(self, start_date: str = '2020-01-01', end_date: str = '2024-12-31'):
        """
        初始化策略
        
        参数:
        start_date: 策略开始日期
        end_date: 策略结束日期
        """
        self.start_date = start_date
        self.end_date = end_date
        self.results_folder = Path('Results')
        self.results_folder.mkdir(exist_ok=True)
        
        # 筛选条件参数
        self.min_stock_price = 4.0  # 正股收盘价 ≥ 4元
        self.min_bond_balance = 5e8  # 转债余额 ≥ 5亿元
        self.min_market_cap = 40e8  # 正股市值 ≥ 40亿元
        self.min_remaining_years = 1.0  # 转债剩余期限 ≥ 1年
        self.min_rating_score = self._get_rating_score('A+')  # 转债评级 ≥ A+
        self.ytm_top_pct = 0.2  # YTM排名前20%
        self.min_avg_amount = 1000e4  # 20日均成交额 ≥ 1000万元
        
        print(f"可转债量化策略初始化完成")
        print(f"策略期间: {start_date} 至 {end_date}")
        print(f"筛选条件:")
        print(f"  - 正股收盘价 ≥ {self.min_stock_price}元")
        print(f"  - 转债余额 ≥ {self.min_bond_balance/1e8:.0f}亿元")
        print(f"  - 正股市值 ≥ {self.min_market_cap/1e8:.0f}亿元")
        print(f"  - 转债剩余期限 ≥ {self.min_remaining_years}年")
        print(f"  - 转债评级 ≥ A+")
        print(f"  - YTM排名前{self.ytm_top_pct*100:.0f}%")
        print(f"  - 排除亏损股票")
        print(f"  - 20日均成交额 ≥ {self.min_avg_amount/1e4:.0f}万元")
    
    def _get_rating_score(self, rating: str) -> int:
        """
        将评级转换为数值分数，用于比较
        评级从高到低：AAA, AA+, AA, AA-, A+, A, A-, BBB+, BBB, BBB-, ...
        """
        rating_map = {
            'AAA': 100, 'AA+': 90, 'AA': 85, 'AA-': 80,
            'A+': 75, 'A': 70, 'A-': 65,
            'BBB+': 60, 'BBB': 55, 'BBB-': 50,
            'BB+': 45, 'BB': 40, 'BB-': 35,
            'B+': 30, 'B': 25, 'B-': 20,
            'CCC': 15, 'CC': 10, 'C': 5, 'D': 0
        }
        return rating_map.get(rating, 0)
    
    def get_convertible_bonds_list(self) -> List[str]:
        """获取所有可转债代码列表"""
        print("获取可转债代码列表...")
        
        try:
            # 从债券基本资料表获取可转债
            keywords = ['S_INFO_WINDCODE', 'S_INFO_NAME', 'B_INFO_SPECIALBONDTYPE']
            conditions = {
                'OR': [
                    {'B_INFO_SPECIALBONDTYPE': ('LIKE', '%可转债%')},
                    {'B_INFO_SPECIALBONDTYPE': ('LIKE', '%可转换%')},
                    {'B_INFO_SPECIALBONDTYPE': ('LIKE', '%转债%')}
                ]
            }
            
            df_bonds = get_db_data('CBondDescription', keywords=keywords, additional_conditions=conditions)
            
            if df_bonds is not None and not df_bonds.empty:
                # 筛选SZ和SH后缀的可转债
                pattern = r'\.(SZ|SH)$'
                mask = df_bonds['S_INFO_WINDCODE'].str.contains(pattern, regex=True, na=False)
                df_bonds = df_bonds[mask]
                
                bond_codes = df_bonds['S_INFO_WINDCODE'].tolist()
                print(f"获取到 {len(bond_codes)} 个可转债代码")
                return bond_codes
            else:
                print("未获取到可转债数据")
                return []
                
        except Exception as e:
            print(f"获取可转债代码列表失败: {e}")
            return []
    
    def get_trading_dates(self) -> List[str]:
        """获取交易日历"""
        print("获取交易日历...")

        try:
            start_date_str = self.start_date.replace('-', '')
            end_date_str = self.end_date.replace('-', '')

            conditions = {
                'S_INFO_EXCHMARKET': 'SSE',
                'TRADE_DAYS': [start_date_str, end_date_str]
            }

            calendar_data = get_db_data('AShareCalendar',
                                      keywords=['TRADE_DAYS'],
                                      additional_conditions=conditions)

            if calendar_data is not None and not calendar_data.empty:
                trade_dates = sorted(calendar_data['TRADE_DAYS'].tolist())
                print(f"获取到 {len(trade_dates)} 个交易日")
                return trade_dates
            else:
                print("未获取到交易日历数据")
                return []

        except Exception as e:
            print(f"获取交易日历失败: {e}")
            return []
    
    def get_month_end_dates(self, trade_dates: List[str]) -> List[str]:
        """获取月末交易日"""
        if not trade_dates:
            return []
        
        # 转换为datetime格式
        dates_df = pd.DataFrame({'date': pd.to_datetime(trade_dates)})
        dates_df = dates_df.set_index('date')
        
        # 获取每月最后一个交易日
        month_ends = dates_df.resample('M').last().index
        month_end_dates = [date.strftime('%Y%m%d') for date in month_ends]
        
        print(f"获取到 {len(month_end_dates)} 个月末交易日")
        return month_end_dates
    
    def get_convertible_bond_data(self, bond_codes: List[str], date: str) -> pd.DataFrame:
        """获取指定日期的可转债数据"""
        print(f"获取 {date} 的可转债数据...")

        all_data = []

        # 1. 获取可转债基础信息和衍生指标
        try:
            cb_keywords = ['S_INFO_WINDCODE', 'TRADE_DT', 'CB_ANAL_YTM', 'CB_ANAL_PTM',
                          'CB_ANAL_CONVPRICE', 'CB_ANAL_CONVVALUE']
            cb_conditions = {
                'S_INFO_WINDCODE': bond_codes,
                'TRADE_DT': date
            }

            cb_data = get_db_data('CCBondValuation', keywords=cb_keywords, additional_conditions=cb_conditions)

            if cb_data is None or cb_data.empty:
                print(f"未获取到 {date} 的可转债衍生指标数据")
                return pd.DataFrame()

            print(f"获取到 {len(cb_data)} 条可转债衍生指标数据")
            all_data.append(cb_data)

        except Exception as e:
            print(f"获取可转债衍生指标数据失败: {e}")
            return pd.DataFrame()

        # 2. 获取可转债余额数据
        try:
            balance_keywords = ['S_INFO_WINDCODE', 'S_INFO_CHANGEDATE', 'B_INFO_OUTSTANDINGBALANCE']
            balance_conditions = {
                'S_INFO_WINDCODE': bond_codes,
                'S_INFO_CHANGEDATE': ('<=', date)
            }

            balance_data = get_db_data('CCBondAmount', keywords=balance_keywords, additional_conditions=balance_conditions)

            if balance_data is not None and not balance_data.empty:
                # 获取每个转债最新的余额数据
                balance_data = balance_data.sort_values('S_INFO_CHANGEDATE').groupby('S_INFO_WINDCODE').last().reset_index()
                balance_data['B_INFO_OUTSTANDINGBALANCE'] = pd.to_numeric(balance_data['B_INFO_OUTSTANDINGBALANCE'], errors='coerce') * 10000  # 万元转元
                print(f"获取到 {len(balance_data)} 条可转债余额数据")
                all_data.append(balance_data[['S_INFO_WINDCODE', 'B_INFO_OUTSTANDINGBALANCE']])

        except Exception as e:
            print(f"获取可转债余额数据失败: {e}")

        # 3. 获取可转债评级数据
        try:
            rating_keywords = ['S_INFO_WINDCODE', 'ANN_DT', 'B_INFO_CREDITRATING']
            rating_conditions = {
                'S_INFO_WINDCODE': bond_codes,
                'ANN_DT': ('<=', date)
            }

            rating_data = get_db_data('CBondRating', keywords=rating_keywords, additional_conditions=rating_conditions)

            if rating_data is not None and not rating_data.empty:
                # 获取每个转债最新的评级数据
                rating_data = rating_data.sort_values('ANN_DT').groupby('S_INFO_WINDCODE').last().reset_index()
                print(f"获取到 {len(rating_data)} 条可转债评级数据")
                all_data.append(rating_data[['S_INFO_WINDCODE', 'B_INFO_CREDITRATING']])

        except Exception as e:
            print(f"获取可转债评级数据失败: {e}")

        # 合并所有数据
        if len(all_data) > 0:
            result_df = all_data[0]
            for df in all_data[1:]:
                result_df = pd.merge(result_df, df, on='S_INFO_WINDCODE', how='left')

            print(f"合并后得到 {len(result_df)} 条可转债数据")
            return result_df
        else:
            return pd.DataFrame()
    
    def get_stock_data(self, stock_codes: List[str], date: str, trade_dates: List[str]) -> pd.DataFrame:
        """获取正股数据"""
        if not stock_codes:
            return pd.DataFrame()
        
        print(f"获取 {date} 的正股数据...")
        
        all_stock_data = []
        
        # 1. 获取正股价格数据
        try:
            price_keywords = ['S_INFO_WINDCODE', 'TRADE_DT', 'S_DQ_CLOSE']
            price_conditions = {
                'S_INFO_WINDCODE': stock_codes,
                'TRADE_DT': date
            }
            
            price_data = get_db_data('AShareEODPrices', keywords=price_keywords, additional_conditions=price_conditions)
            
            if price_data is not None and not price_data.empty:
                print(f"获取到 {len(price_data)} 条正股价格数据")
                all_stock_data.append(price_data)
            
        except Exception as e:
            print(f"获取正股价格数据失败: {e}")
        
        # 2. 获取正股市值数据
        try:
            mv_keywords = ['S_INFO_WINDCODE', 'TRADE_DT', 'S_VAL_MV_ARD']
            mv_conditions = {
                'S_INFO_WINDCODE': stock_codes,
                'TRADE_DT': date
            }
            
            mv_data = get_db_data('AShareValuationIndicator', keywords=mv_keywords, additional_conditions=mv_conditions)
            
            if mv_data is not None and not mv_data.empty:
                print(f"获取到 {len(mv_data)} 条正股市值数据")
                all_stock_data.append(mv_data)
            
        except Exception as e:
            print(f"获取正股市值数据失败: {e}")
        
        # 合并正股数据
        if len(all_stock_data) > 0:
            result_df = all_stock_data[0]
            for df in all_stock_data[1:]:
                result_df = pd.merge(result_df, df, on=['S_INFO_WINDCODE', 'TRADE_DT'], how='left')

            print(f"合并后得到 {len(result_df)} 条正股数据")
            return result_df
        else:
            return pd.DataFrame()

    def get_stock_financial_data(self, stock_codes: List[str], date: str) -> pd.DataFrame:
        """获取正股财务数据（净利润）"""
        if not stock_codes:
            return pd.DataFrame()

        print(f"获取 {date} 的正股财务数据...")

        try:
            # 获取最近的年报或半年报数据
            report_date = date[:4] + '1231'  # 先尝试当年年报
            if date[4:] < '0630':  # 如果是上半年，使用上一年年报
                report_date = str(int(date[:4]) - 1) + '1231'

            financial_keywords = ['S_INFO_WINDCODE', 'REPORT_PERIOD', 'NET_PROFIT_EXCL_MIN_INT_INC']
            financial_conditions = {
                'S_INFO_WINDCODE': stock_codes,
                'REPORT_PERIOD': ('<=', report_date),
                'STATEMENT_TYPE': '408001000'  # 合并报表
            }

            financial_data = get_db_data('AShareIncome', keywords=financial_keywords, additional_conditions=financial_conditions)

            if financial_data is not None and not financial_data.empty:
                # 获取每个股票最新的财务数据
                financial_data = financial_data.sort_values('REPORT_PERIOD').groupby('S_INFO_WINDCODE').last().reset_index()
                print(f"获取到 {len(financial_data)} 条正股财务数据")
                return financial_data[['S_INFO_WINDCODE', 'NET_PROFIT_EXCL_MIN_INT_INC']]
            else:
                print("未获取到正股财务数据")
                return pd.DataFrame()

        except Exception as e:
            print(f"获取正股财务数据失败: {e}")
            return pd.DataFrame()

    def get_stock_volume_data(self, stock_codes: List[str], date: str, trade_dates: List[str]) -> pd.DataFrame:
        """获取正股20日平均成交额数据"""
        if not stock_codes:
            return pd.DataFrame()

        print(f"获取 {date} 前20日的正股成交额数据...")

        try:
            # 获取当前日期在交易日列表中的位置
            if date not in trade_dates:
                print(f"日期 {date} 不在交易日列表中")
                return pd.DataFrame()

            date_idx = trade_dates.index(date)
            start_idx = max(0, date_idx - 19)  # 获取前20个交易日
            volume_dates = trade_dates[start_idx:date_idx + 1]

            # 限制查询的股票数量，分批处理
            batch_size = 20
            all_volume_data = []

            for i in range(0, len(stock_codes), batch_size):
                batch_codes = stock_codes[i:i + batch_size]
                print(f"处理第 {i//batch_size + 1} 批股票 ({len(batch_codes)} 只)")

                volume_keywords = ['S_INFO_WINDCODE', 'TRADE_DT', 'S_DQ_AMOUNT']
                volume_conditions = {
                    'S_INFO_WINDCODE': batch_codes,
                    'TRADE_DT': volume_dates
                }

                batch_data = get_db_data('AShareEODPrices', keywords=volume_keywords, additional_conditions=volume_conditions)

                if batch_data is not None and not batch_data.empty:
                    all_volume_data.append(batch_data)

            if all_volume_data:
                volume_data = pd.concat(all_volume_data, ignore_index=True)

                # 计算20日平均成交额
                volume_data['S_DQ_AMOUNT'] = pd.to_numeric(volume_data['S_DQ_AMOUNT'], errors='coerce') * 1000  # 千元转元
                volume_data = volume_data.dropna(subset=['S_DQ_AMOUNT'])

                if not volume_data.empty:
                    avg_volume = volume_data.groupby('S_INFO_WINDCODE')['S_DQ_AMOUNT'].mean().reset_index()
                    avg_volume.columns = ['S_INFO_WINDCODE', 'AVG_20D_AMOUNT']

                    print(f"计算得到 {len(avg_volume)} 条正股20日平均成交额数据")
                    return avg_volume
                else:
                    print("成交额数据处理后为空")
                    return pd.DataFrame()
            else:
                print("未获取到正股成交额数据")
                return pd.DataFrame()

        except Exception as e:
            print(f"获取正股成交额数据失败: {e}")
            return pd.DataFrame()

    def extract_stock_code(self, bond_code: str) -> str:
        """从可转债代码提取正股代码"""
        # 简化的映射逻辑，实际应该查询数据库获取准确的正股代码
        if bond_code.endswith('.SH'):
            # 上交所可转债，通常是6位数字
            if bond_code.startswith('11'):
                # 假设110xxx对应600xxx
                stock_code = '6' + bond_code[2:5] + '00.SH'
            else:
                stock_code = bond_code  # 需要实际查询映射关系
        elif bond_code.endswith('.SZ'):
            # 深交所可转债
            if bond_code.startswith('12'):
                # 假设12xxxx对应00xxxx或30xxxx
                stock_code = '00' + bond_code[2:6] + '.SZ'
            else:
                stock_code = bond_code  # 需要实际查询映射关系
        else:
            stock_code = bond_code

        return stock_code

    def get_bond_stock_mapping(self, bond_codes: List[str]) -> Dict[str, str]:
        """获取可转债与正股的映射关系"""
        print("获取可转债与正股的映射关系...")

        try:
            mapping_keywords = ['S_INFO_WINDCODE', 'S_INFO_COMPCODE']
            mapping_conditions = {
                'S_INFO_WINDCODE': bond_codes
            }

            mapping_data = get_db_data('CCBondIssuance', keywords=mapping_keywords, additional_conditions=mapping_conditions)

            if mapping_data is not None and not mapping_data.empty:
                # 获取公司代码到股票代码的映射
                comp_keywords = ['S_INFO_COMPCODE', 'S_INFO_WINDCODE']
                comp_conditions = {
                    'S_INFO_COMPCODE': mapping_data['S_INFO_COMPCODE'].tolist()
                }

                comp_data = get_db_data('AShareDescription', keywords=comp_keywords, additional_conditions=comp_conditions)

                if comp_data is not None and not comp_data.empty:
                    # 创建映射字典
                    comp_to_stock = dict(zip(comp_data['S_INFO_COMPCODE'], comp_data['S_INFO_WINDCODE']))
                    bond_to_stock = {}

                    for _, row in mapping_data.iterrows():
                        bond_code = row['S_INFO_WINDCODE']
                        comp_code = row['S_INFO_COMPCODE']
                        if comp_code in comp_to_stock:
                            bond_to_stock[bond_code] = comp_to_stock[comp_code]

                    print(f"获取到 {len(bond_to_stock)} 个可转债-正股映射关系")
                    return bond_to_stock
                else:
                    print("未获取到公司股票映射数据")
                    return {}
            else:
                print("未获取到可转债发行数据")
                return {}

        except Exception as e:
            print(f"获取映射关系失败: {e}")
            return {}

    def apply_screening_conditions(self, df: pd.DataFrame) -> pd.DataFrame:
        """应用所有筛选条件"""
        if df.empty:
            return df

        print(f"开始筛选，初始数据: {len(df)} 条")

        # 数据类型转换和清理
        numeric_columns = ['S_DQ_CLOSE', 'B_INFO_OUTSTANDINGBALANCE', 'S_VAL_MV_ARD',
                          'CB_ANAL_PTM', 'CB_ANAL_YTM', 'NET_PROFIT_EXCL_MIN_INT_INC', 'AVG_20D_AMOUNT']

        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # 1. 正股收盘价 ≥ 4元
        if 'S_DQ_CLOSE' in df.columns:
            df = df.dropna(subset=['S_DQ_CLOSE'])
            df = df[df['S_DQ_CLOSE'] >= self.min_stock_price]
            print(f"正股价格筛选后: {len(df)} 条")

        # 2. 转债余额 ≥ 5亿元
        if 'B_INFO_OUTSTANDINGBALANCE' in df.columns:
            df = df.dropna(subset=['B_INFO_OUTSTANDINGBALANCE'])
            df = df[df['B_INFO_OUTSTANDINGBALANCE'] >= self.min_bond_balance]
            print(f"转债余额筛选后: {len(df)} 条")

        # 3. 正股市值 ≥ 40亿元
        if 'S_VAL_MV_ARD' in df.columns:
            df = df.dropna(subset=['S_VAL_MV_ARD'])
            df = df[df['S_VAL_MV_ARD'] >= self.min_market_cap]
            print(f"正股市值筛选后: {len(df)} 条")

        # 4. 转债剩余期限 ≥ 1年
        if 'CB_ANAL_PTM' in df.columns:
            df = df.dropna(subset=['CB_ANAL_PTM'])
            df = df[df['CB_ANAL_PTM'] >= self.min_remaining_years]
            print(f"剩余期限筛选后: {len(df)} 条")

        # 5. 转债评级 ≥ A+
        if 'B_INFO_CREDITRATING' in df.columns:
            df = df.dropna(subset=['B_INFO_CREDITRATING'])
            df['RATING_SCORE'] = df['B_INFO_CREDITRATING'].apply(self._get_rating_score)
            df = df[df['RATING_SCORE'] >= self.min_rating_score]
            print(f"评级筛选后: {len(df)} 条")

        # 7. 排除亏损股票
        if 'NET_PROFIT_EXCL_MIN_INT_INC' in df.columns:
            df = df.dropna(subset=['NET_PROFIT_EXCL_MIN_INT_INC'])
            df = df[df['NET_PROFIT_EXCL_MIN_INT_INC'] > 0]
            print(f"排除亏损股票后: {len(df)} 条")

        # 8. 20日均成交额 ≥ 1000万元
        if 'AVG_20D_AMOUNT' in df.columns:
            df = df.dropna(subset=['AVG_20D_AMOUNT'])
            df = df[df['AVG_20D_AMOUNT'] >= self.min_avg_amount]
            print(f"成交额筛选后: {len(df)} 条")

        # 6. YTM排名前20%（在所有筛选条件应用后）
        if 'CB_ANAL_YTM' in df.columns and len(df) > 0:
            df = df.dropna(subset=['CB_ANAL_YTM'])
            if len(df) > 0:
                ytm_threshold = df['CB_ANAL_YTM'].quantile(1 - self.ytm_top_pct)
                df = df[df['CB_ANAL_YTM'] >= ytm_threshold]
                print(f"YTM前20%筛选后: {len(df)} 条")

        return df

    def select_bonds_for_date(self, date: str, trade_dates: List[str]) -> List[str]:
        """为指定日期选择符合条件的可转债"""
        print(f"\n=== 处理日期: {date} ===")

        # 获取可转债列表
        bond_codes = self.get_convertible_bonds_list()
        if not bond_codes:
            return []

        # 获取可转债数据
        cb_data = self.get_convertible_bond_data(bond_codes, date)
        if cb_data.empty:
            return []

        # 获取可转债与正股的映射关系
        bond_stock_mapping = self.get_bond_stock_mapping(bond_codes)
        if not bond_stock_mapping:
            print("未获取到映射关系，使用简化映射")
            bond_stock_mapping = {bond: self.extract_stock_code(bond) for bond in bond_codes}

        # 为可转债数据添加正股代码
        cb_data['STOCK_CODE'] = cb_data['S_INFO_WINDCODE'].map(bond_stock_mapping)
        cb_data = cb_data.dropna(subset=['STOCK_CODE'])

        if cb_data.empty:
            return []

        # 获取正股数据
        stock_codes = cb_data['STOCK_CODE'].unique().tolist()
        stock_data = self.get_stock_data(stock_codes, date, trade_dates)
        financial_data = self.get_stock_financial_data(stock_codes, date)
        volume_data = self.get_stock_volume_data(stock_codes, date, trade_dates)

        # 合并所有数据
        merged_data = cb_data.copy()

        if not stock_data.empty:
            stock_data_renamed = stock_data.rename(columns={'S_INFO_WINDCODE': 'STOCK_CODE'})
            merged_data = pd.merge(merged_data, stock_data_renamed, on='STOCK_CODE', how='left')

        if not financial_data.empty:
            financial_data_renamed = financial_data.rename(columns={'S_INFO_WINDCODE': 'STOCK_CODE'})
            merged_data = pd.merge(merged_data, financial_data_renamed, on='STOCK_CODE', how='left')

        if not volume_data.empty:
            volume_data_renamed = volume_data.rename(columns={'S_INFO_WINDCODE': 'STOCK_CODE'})
            merged_data = pd.merge(merged_data, volume_data_renamed, on='STOCK_CODE', how='left')

        # 应用筛选条件
        selected_data = self.apply_screening_conditions(merged_data)

        if selected_data.empty:
            print(f"{date}: 未找到符合条件的可转债")
            return []

        # 按YTM降序排列，选择所有符合条件的
        selected_data = selected_data.sort_values('CB_ANAL_YTM', ascending=False)
        selected_bonds = selected_data['S_INFO_WINDCODE'].tolist()

        print(f"{date}: 选择了 {len(selected_bonds)} 只可转债")
        return selected_bonds

    def get_bond_prices(self, bond_codes: List[str], dates: List[str]) -> pd.DataFrame:
        """获取可转债价格数据"""
        print("获取可转债价格数据...")

        try:
            price_keywords = ['S_INFO_WINDCODE', 'TRADE_DT', 'S_DQ_CLOSE']
            price_conditions = {
                'S_INFO_WINDCODE': bond_codes,
                'TRADE_DT': dates
            }

            price_data = get_db_data('CBondEODPrices', keywords=price_keywords, additional_conditions=price_conditions)

            if price_data is not None and not price_data.empty:
                print(f"获取到 {len(price_data)} 条可转债价格数据")
                return price_data
            else:
                print("未获取到可转债价格数据")
                return pd.DataFrame()

        except Exception as e:
            print(f"获取可转债价格数据失败: {e}")
            return pd.DataFrame()

    def run_strategy(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """运行策略"""
        print("开始运行可转债量化策略...")

        # 获取交易日历
        trade_dates = self.get_trading_dates()
        if not trade_dates:
            print("未获取到交易日历，策略终止")
            return pd.DataFrame(), pd.DataFrame()

        # 获取月末调仓日期
        rebalance_dates = self.get_month_end_dates(trade_dates)
        if not rebalance_dates:
            print("未获取到调仓日期，策略终止")
            return pd.DataFrame(), pd.DataFrame()

        # 存储每期选择的债券
        positions = {}
        all_selected_bonds = set()

        # 逐期选择债券
        for date in rebalance_dates:
            selected_bonds = self.select_bonds_for_date(date, trade_dates)
            positions[date] = selected_bonds
            all_selected_bonds.update(selected_bonds)

        if not all_selected_bonds:
            print("未选择到任何可转债，策略终止")
            return pd.DataFrame(), pd.DataFrame()

        print(f"策略期间共涉及 {len(all_selected_bonds)} 只可转债")

        # 获取所有涉及债券的价格数据
        price_data = self.get_bond_prices(list(all_selected_bonds), trade_dates)
        if price_data.empty:
            print("未获取到价格数据，策略终止")
            return pd.DataFrame(), pd.DataFrame()

        # 构建价格矩阵
        price_pivot = price_data.pivot(index='TRADE_DT', columns='S_INFO_WINDCODE', values='S_DQ_CLOSE')
        price_pivot = price_pivot.reindex(trade_dates).fillna(method='ffill')

        # 构建持仓矩阵
        position_matrix = pd.DataFrame(0, index=trade_dates, columns=price_pivot.columns)

        for i, date in enumerate(rebalance_dates):
            if date in trade_dates:
                selected_bonds = positions[date]
                if selected_bonds:
                    # 等权重配置
                    weight = 1.0 / len(selected_bonds)

                    # 找到下一个调仓日期
                    if i < len(rebalance_dates) - 1:
                        next_date = rebalance_dates[i + 1]
                        date_range = [d for d in trade_dates if date <= d < next_date]
                    else:
                        date_range = [d for d in trade_dates if d >= date]

                    # 设置持仓权重
                    for d in date_range:
                        if d in position_matrix.index:
                            for bond in selected_bonds:
                                if bond in position_matrix.columns:
                                    position_matrix.loc[d, bond] = weight

        print("策略运行完成")
        return price_pivot, position_matrix

    def calculate_strategy_returns(self, price_data: pd.DataFrame, position_data: pd.DataFrame) -> pd.DataFrame:
        """计算策略收益率"""
        print("计算策略收益率...")

        # 计算日收益率
        returns = price_data.pct_change().fillna(0)

        # 计算策略收益率
        strategy_returns = (returns * position_data.shift(1)).sum(axis=1)

        # 计算累计收益率
        cumulative_returns = (1 + strategy_returns).cumprod()

        # 构建结果DataFrame
        result_df = pd.DataFrame({
            'date': returns.index,
            'daily_return': strategy_returns.values,
            'cumulative_return': cumulative_returns.values,
            'net_value': cumulative_returns.values
        })

        result_df['date'] = pd.to_datetime(result_df['date'])

        print("策略收益率计算完成")
        return result_df

    def save_results(self, returns_df: pd.DataFrame, position_df: pd.DataFrame):
        """保存结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 保存策略收益率
        returns_file = self.results_folder / f'月频转债策略收益率_{timestamp}.xlsx'
        returns_df.to_excel(returns_file, index=False)
        print(f"策略收益率已保存至: {returns_file}")

        # 保存持仓数据
        position_file = self.results_folder / f'月频转债策略持仓_{timestamp}.xlsx'
        position_df.to_excel(position_file)
        print(f"持仓数据已保存至: {position_file}")

        return returns_file, position_file

    def run_backtest(self):
        """运行完整的回测"""
        print("=" * 60)
        print("开始运行月频可转债量化策略回测")
        print("=" * 60)

        try:
            # 运行策略
            price_data, position_data = self.run_strategy()

            if price_data.empty or position_data.empty:
                print("策略运行失败，无法进行回测")
                return

            # 计算策略收益率
            returns_df = self.calculate_strategy_returns(price_data, position_data)

            # 保存结果
            returns_file, position_file = self.save_results(returns_df, position_data)

            # 使用StrategyAnalyzer进行分析
            print("\n开始策略分析...")
            analyzer = StrategyAnalyzer()

            # 准备数据格式
            analysis_data = returns_df.copy()
            analysis_data = analysis_data.set_index('date')
            analysis_data.index.name = 'DATE'
            analysis_data['STRATEGY'] = analysis_data['net_value']

            # 进行策略分析
            analyzer.analyze_strategy(
                strategy_data=analysis_data,
                strategy_name="月频可转债量化策略",
                benchmark_name="基准",
                save_path=str(returns_file.with_suffix(''))
            )

            print("\n" + "=" * 60)
            print("回测完成！")
            print(f"策略收益率文件: {returns_file}")
            print(f"持仓数据文件: {position_file}")
            print("=" * 60)

        except Exception as e:
            print(f"回测过程中发生错误: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    print("月频可转债量化策略")
    print("=" * 50)

    # 设置策略参数
    start_date = '2020-01-01'
    end_date = '2024-12-31'

    # 创建策略实例
    strategy = ConvertibleBondStrategy(start_date=start_date, end_date=end_date)

    # 运行回测
    strategy.run_backtest()


if __name__ == "__main__":
    main()
